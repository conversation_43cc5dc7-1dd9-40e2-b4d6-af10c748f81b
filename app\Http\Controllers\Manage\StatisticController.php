<?php

namespace App\Http\Controllers\Manage;

use App\Constants;
use App\Models\Exam;
use App\Models\ExamResultFinal;
use App\Models\ExamStatistic;
use App\Models\Region;
use App\Models\Subject;
use App\Models\UserGroup;
use App\Models\UserOffice;
use App\Models\ExamResult;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Web\ExamsController;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class StatisticController extends Controller
{
    public function index($type)
    {
        $orgTypeId = getOrgTypeId();
        if ($orgTypeId == 0) {
            exit;
        }
        $exams = Exam::orderBy('status', 'desc')->orderBy('date_start', 'desc')->get(['id', 'name']);
        $offices = UserOffice::orderBy('name', 'asc')->get(["id", "name"]);
        return view('manage.statistic.index', ['exams' => $exams, 'type' => $type, "offices" => $offices]);
    }

    public function statistic($type, Request $request)
    {
        $orgTypeId = getOrgTypeId();
        if ($orgTypeId == 0) {
            exit;
        }

        $examId = isset($request->exam_id) ? $request->exam_id : 0;
        $docType = isset($request->doc_type) ? $request->doc_type : "html";
        switch ($type) {
            case "candidates":
                $orderType = isset($request->order) ? $request->order : "asc";
                $orderTop = isset($request->order_top) ? $request->order_top : 10;
                return self::statisticCandidates($examId, $orderType, $docType, $orderTop);
                break;
            case "subjects":
                $officeId = isset($request->office_id) ? $request->office_id : 0;
                return self::statisticSubjects($examId, $docType, $officeId);
                break;
        }
    }

    public function statisticCandidates($examId, $orderType, $docType, $orderTop)
    {
        $examCtrl = new ExamController();
        $fileName = "tk-chi-tiet-diem.xlsx";
        $objPHPExcel = IOFactory::load(resource_path("templates/$fileName"));
        $sheet = $objPHPExcel->getSheet(0);
        $sheet->setShowGridlines(false);
        $exam = Exam::find($examId, ['id', 'name']);
        if (!isset($exam)) {
            return ["success" => false, "msg" => "Không tồn tại kỳ thi"];
        }
        $examName = isset($exam->name) ? $exam->name : "";
        $sheet->setCellValue("A1", mb_strtoupper($examName, "UTF8"));

        $sql = "select office_name, name";
        $subSQL2 = "";

        $subjects = $examCtrl->getSubjects($examId);
        $countMultipleChoice = 0;
        $countLiterature = 0;
        $subCol = 67;
        $colSubjectNames = [];
        $markPerQuestion = getExamConfig($examId, 'mark_per_multiple_choice_question');

        foreach ($subjects as $sub) {
            $subName = $sub->name;
            if ($sub->number_of_multiple_choice_questions > 0) {
                $countMultipleChoice++;
                $subCol++;
                $sheet->setCellValue(chr($subCol)."3", mb_strtoupper($subName, "UTF8"));
                $colSubjectNames[] = $subName;
            }

            if ($sub->number_of_literature_questions > 0) {
                $countLiterature += $sub->number_of_literature_questions;
            }
        }

        foreach ($colSubjectNames as $colSubName) {
            $sql .= ", max($colSubName) as $colSubName";
            $subSQL2 .= ", if(ex_subjects.name='$colSubName',
                ex_result_marks.mark*$markPerQuestion, null) as $colSubName ";
        }

        $sql .= " , max(total_mark) as total_mark, literature_marks_str from (
                select user_offices.name as office_name, users.name, ex_subjects.name as subject_name
                $subSQL2, ex_results.literature_marks_str,
                ifnull(ex_results.mark*$markPerQuestion, 0) +  ifnull(ex_results.literature_mark, 0) as total_mark,
                ROW_NUMBER() OVER (
                    PARTITION BY ex_results.user_id
                    ORDER BY ifnull(ex_results.mark*$markPerQuestion, 0) + ifnull(ex_results.literature_mark, 0) desc
                ) row_num
                from ex_results
                inner join users on users.id=ex_results.user_id
                inner join user_profiles on user_profiles.user_id=users.id
                inner join user_offices on user_offices.id=user_profiles.office_id
                left join ex_result_marks on ex_result_marks.result_id=ex_results.id
                left join ex_subjects on ex_subjects.id=ex_result_marks.subject_id and ex_subjects.deleted_at is null
                where ex_results.deleted_at is null and users.deleted_at is null
                and ex_results.exam_id=$examId and users.organization_id=0
            ) tb  where tb.row_num <= $countMultipleChoice
            group by office_name, name, literature_marks_str order by total_mark $orderType ";

        if ($orderTop > 0) {
            $sql .= " limit $orderTop";
        }

        $data = DB::select($sql);

        if ($countMultipleChoice > 0) {
            $sheet->mergeCells("D2:" . chr($subCol) . "2");
            $sheet->setCellValue("D2", mb_strtoupper("PHÒNG BAN", "UTF8"));
        }

        if ($countLiterature > 0) {
            $sheet->mergeCells(chr($subCol + 1)."2:" . chr($subCol + $countLiterature) . "2");
            $sheet->setCellValue(chr($subCol + 1)."2", mb_strtoupper("TỰ LUẬN", "UTF8"));
            for ($i = 1; $i <= $countLiterature; $i++) {
                $sheet->setCellValue(chr($subCol + $i)."3", mb_strtoupper("CÂU $i", "UTF8"));
            }
        }

        $maxCol = chr($subCol + $countLiterature + 1);
        $sheet->setCellValue($maxCol."2", mb_strtoupper("TỔNG ĐIỂM", "UTF8"));
        $sheet->mergeCells($maxCol."2:$maxCol" . "3");
        $sheet->mergeCells("A1:{$maxCol}1");

        $row = 3;
        foreach ($data as $d) {
            $row++;
            $sheet->setCellValue("A$row", $row - 3);
            $sheet->setCellValue("B$row", $d->name);
            $sheet->setCellValue("C$row", $d->office_name);
            $startCol = 67;
            foreach ($colSubjectNames as $cSubName) {
                $startCol++;
                $sheet->setCellValue(chr($startCol)."$row", $d->$cSubName);
            }

            if (!empty($d->literature_marks_str)) {
                $litMarks = explode(",", $d->literature_marks_str);
                foreach ($litMarks as $lMark) {
                    $startCol++;
                    $sheet->setCellValue(chr($startCol) . "$row", $lMark);
                }
            }
            $sheet->setCellValue("$maxCol{$row}", $d->total_mark);
        }

        $styleCenter = array(
            'alignment' => array(
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            )
        );
        $sheet->getStyle($maxCol."4:$maxCol{$row}")->applyFromArray($styleCenter);

        $style = [
            "borders" => [
                "allBorders" => ["borderStyle" => Border::BORDER_THIN]
            ]
        ];
        $sheet->getStyle("A2:$maxCol{$row}")->applyFromArray($style);

        $fileType = "Html";
        if ($docType == "excel") {
            $fileType = "Xlsx";
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename=' . $fileName);
            header('Cache-Control: max-age=0');
        }
        $objWriter = IOFactory::createWriter($objPHPExcel, $fileType);
        $objWriter->save('php://output');
        exit;
    }

    public function statisticSubjects($examId, $docType, $officeId)
    {
        $fileName = "tk-diem-tb-theo-don-vi.xlsx";
        $objPHPExcel = IOFactory::load(resource_path("templates/$fileName"));
        $sheet = $objPHPExcel->getSheet(0);
        $sheet->setShowGridlines(false);
        $exam = Exam::find($examId, ['id', 'name']);
        if (!isset($exam)) {
            return ["success" => false, "msg" => "Không tồn tại kỳ thi"];
        }
        $examName = isset($exam->name) ? $exam->name : "";
        $sheet->setCellValue("A1", mb_strtoupper($examName, "UTF8"));
        $markPerQuestion = getExamConfig($examId, 'mark_per_multiple_choice_question');

        if ($officeId == 0) {
            $sql = "select user_offices.id, user_offices.name, tb.so_nguoi_thi, tb.so_luot_thi,
              tb.mark, tb.mark/tb.so_luot_thi as diem_trung_binh
            from user_offices
            left join (
                select count(distinct rs.user_id) as so_nguoi_thi, count(rs.user_id) as so_luot_thi,
                sum(rs.mark*$markPerQuestion) as mark,
                case 
                    when off.parent_id is null then off.id 
                    else substr(off.path_primary_key, 1, position('-' in off.path_primary_key) - 1) 
                end as root_id
                    from ex_results rs
                    inner join ex_exams ex on ex.id=rs.exam_id
                    inner join users u on u.id=rs.user_id
                    inner join user_profiles up on up.user_id=u.id
                    inner join user_offices off on off.id=up.office_id
                where rs.deleted_at is null and u.deleted_at is null and ex.deleted_at is null
                 and u.organization_id=0 and rs.exam_id=$examId group by root_id
            ) tb on tb.root_id=user_offices.id
            where user_offices.parent_id is null order by tb.mark/tb.so_luot_thi desc";
        } else {
            $sql = "select user_offices.id, user_offices.name, tb.so_nguoi_thi, tb.so_luot_thi,
                tb.mark, tb.mark/tb.so_luot_thi as diem_trung_binh
            from user_offices
            left join (
                select off.id, count(distinct rs.user_id) as so_nguoi_thi,
                    count(rs.user_id) as so_luot_thi, sum(rs.mark*$markPerQuestion) as mark
                    from ex_results rs
                    inner join ex_exams ex on ex.id=rs.exam_id
                    inner join users u on u.id=rs.user_id
                    inner join user_profiles up on up.user_id=u.id
                    inner join user_offices off on off.id=up.office_id
                where rs.deleted_at is null and u.deleted_at is null and ex.deleted_at is null
                 and u.organization_id=0 and rs.exam_id=$examId
                 and concat('-', off.path_primary_key, '-') like '%-$officeId-%' group by off.id
            ) tb on tb.id=user_offices.id
            where concat('-', user_offices.path_primary_key, '-') like '%-$officeId-%'
            order by tb.mark/tb.so_luot_thi desc";
        }

        $data = DB::select($sql);

        $row = 2;
        foreach ($data as $d) {
            $row++;
            $sheet->setCellValue("A$row", $row - 2);
            $sheet->setCellValue("B$row", $d->name);
            $sheet->setCellValue("C$row", $d->so_nguoi_thi);
            $sheet->setCellValue("D$row", $d->so_luot_thi);
            $sheet->setCellValue("E$row", $d->mark);
            $sheet->setCellValue("F$row", "=ROUND($d->diem_trung_binh, 2)");
        }

        $style = [
            "borders" => [
                "allBorders" => ["borderStyle" => Border::BORDER_THIN]
            ]
        ];
        $sheet->getStyle("A3:F$row")->applyFromArray($style);

        $fileType = "Html";
        if ($docType == "excel") {
            $fileType = "Xlsx";
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename=' . $fileName);
            header('Cache-Control: max-age=0');
        }
        $objWriter = IOFactory::createWriter($objPHPExcel, $fileType);
        $objWriter->save('php://output');
        exit;
    }

    public function statisticByOffices($examId, $collectionId, $agesId, $docType)
    {
        $style = [
            "borders" => [
                "allBorders" => ["borderStyle" => Border::BORDER_THIN]
            ]
        ];

        if ($agesId >= 0) {
            $ages = DB::table("grade_ages");
            if ($agesId > 0) {
                $ages = $ages->where('id', $agesId);
            }
            $ages = $ages->orderBy('id', 'asc')->get();
            $count = count($ages);
            $sqlAges = "";
            $whereAge = "";

            if ($agesId == 0) {
                foreach ($ages as $k => $v) {
                    if ($k == 0) {
                        $sqlAges .= " when YEAR(NOW()) - YEAR(date_of_birth) <= $v->max then $v->id";
                    } else {
                        if ($k < $count - 2) {
                            $sqlAges .= " when YEAR(NOW()) - YEAR(date_of_birth) >= $v->min
                                and YEAR(NOW()) - YEAR(up.date_of_birth) <= $v->max then $v->id ";
                        } else {
                            $sqlAges .= $k == $count - 2 ?
                                " when YEAR(NOW()) - YEAR(up.date_of_birth) >= $v->min then $v->id "
                                : " else $v->id ";
                        }
                    }
                }
            } else {
                $v = $ages[0];
                $sqlAges .= " when ";
                if ($v->id == 0) {
                    $sqlAges .= " YEAR(NOW()) - YEAR(date_of_birth) <= $v->max ";
                } else {
                    if ($v->id < 4) {
                        $whereAge .= " YEAR(NOW()) - YEAR(date_of_birth) >= $v->min 
                            and YEAR(NOW()) - YEAR(date_of_birth) <= $v->max ";
                    } else {
                        $whereAge .= $v->id == 4 ? " YEAR(NOW()) - YEAR(date_of_birth) >= $v->min "
                            : " date_of_birth is null ";
                    }
                }

                $sqlAges .= " $whereAge then $v->id ";
            }

            if ($collectionId == 0) {
                $userOffices = UserOffice::whereNull("parent_id")
                    ->select("id", "name")
                    ->orderBy("name", "asc")->get();
            } else {
                $userOffices = UserOffice::where("parent_id", $collectionId)
                    ->orWhere("id", $collectionId)
                    ->select("id", "name")
                    ->orderBy("parent_id", "desc")->orderBy("name", "asc")->get();
            }

            $data = [];
            foreach ($userOffices as $ufs) {
                foreach ($ages as $ag) {
                    $data["-$ufs->id-$ag->id"] = [
                        "id" => $ufs->id,
                        "name" => $ufs->name,
                        "age_name" => $ag->name,
                        "age_id" => $ag->id,
                        "so_nguoi_thi" => 0,
                        "so_luot_thi" => 0,
                        "is_parent" => $ufs->id == $collectionId ? 1 : 0,
                        "path" => Str::slug($ufs->name),
                        "sumOffPerson" => 0,
                        "sumOffSession" => 0
                    ];
                }
            }

            $sql = "select uo.id, count(distinct rs.user_id) as so_nguoi_thi,
                count(rs.id) as so_luot_thi, uo.parent_id, concat('-',uo.path_primary_key,'-') as path_primary_key, ";
            $sql .= $agesId == 0 ? " case $sqlAges end as grade_age_id " : " $agesId as grade_age_id ";
            $sql .= " from ex_results rs
                inner join ex_exams ex on ex.id=rs.exam_id
                inner join users u on u.id=rs.user_id
                inner join user_profiles up on up.user_id=u.id
                inner join user_offices uo on uo.id=up.office_id
                where rs.deleted_at is null and u.deleted_at is null and ex.deleted_at is null
                  and u.organization_id=0 ";

            if ($agesId > 0) {
                $sql .= " and $whereAge ";
            }

            if ($examId > 0) {
                $sql .= " and rs.exam_id=$examId ";
            }

            if ($collectionId > 0) {
                $sql .= " and concat('-', uo.path_primary_key, '-') like '%-$collectionId-%' ";
            }

            $sql .= " group by uo.id, grade_age_id order by uo.path_primary_key, grade_age_id";
            $offices = DB::select($sql);

            $index = 1;
            if ($collectionId > 0) {
                $currentOffice = UserOffice::find($collectionId);
                $index = count(explode("-", isset($currentOffice->path_primary_key) ? $currentOffice->path_primary_key : "")) + 1;
                //$data["-$collectionId-"]['name'] = "Tổng";
            }

            $tongNguoiThi = 0;
            $tongLuotThi = 0;
            $sumOffPerson = 0;
            $sumOffSession = 0;
            $offBeforeId = 0;
            $offCurrentId = 0;
            foreach ($offices as $f) {
                $keys = explode("-", $f->path_primary_key);
                $offCurrentId = (count($keys) >= $index + 2) ? $keys[$index] : $collectionId;

                if ($offCurrentId != $offBeforeId && $offBeforeId > 0) {
                    foreach ($ages as $ag) {
                        $data["-$offBeforeId-$ag->id"]["sumOffPerson"] = $sumOffPerson;
                        $data["-$offBeforeId-$ag->id"]["sumOffSession"] = $sumOffSession;
                    }
                    $sumOffPerson = 0;
                    $sumOffSession = 0;
                }

                $data["-$offCurrentId-$f->grade_age_id"]['so_nguoi_thi'] += $f->so_nguoi_thi;
                $data["-$offCurrentId-$f->grade_age_id"]['so_luot_thi'] += $f->so_luot_thi;
                if ($collectionId > 0 && $f->id != $collectionId) {
                    $data["-$collectionId-$f->grade_age_id"]['so_nguoi_thi'] += $f->so_nguoi_thi;
                    $data["-$collectionId-$f->grade_age_id"]['so_luot_thi'] += $f->so_luot_thi;
                }
                $tongNguoiThi += $f->so_nguoi_thi;
                $tongLuotThi += $f->so_luot_thi;

                $sumOffPerson += $f->so_nguoi_thi;
                $sumOffSession += $f->so_luot_thi;

                $offBeforeId = $offCurrentId;
            }

            /**** add for last office ****/
            if ($offCurrentId > 0) {
                foreach ($ages as $ag) {
                    $data["-$offCurrentId-$ag->id"]["sumOffPerson"] = $sumOffPerson;
                    $data["-$offCurrentId-$ag->id"]["sumOffSession"] = $sumOffSession;
                }
            }

            $isParents = array_column($data, 'is_parent');
            $numOfPersons = array_column($data, 'sumOffPerson');
            $numOfSession = array_column($data, 'sumOffSession');
            $ageIds = array_column($data, 'age_id');
            $paths = array_column($data, 'path');
            array_multisort(
                $isParents,
                SORT_ASC,
                $numOfPersons,
                SORT_DESC,
                $numOfSession,
                SORT_DESC,
                $paths,
                SORT_ASC,
                $ageIds,
                SORT_ASC,
                $data
            );

            $fileName = "tk-theo-dia-phuong.xlsx";
            $objPHPExcel = IOFactory::load(resource_path("templates/$fileName"));
            $sheet = $objPHPExcel->getSheet(0);
            $sheet->setShowGridlines(false);

            /*if ($examId > 0) {
                $exam = Exam::find($examId);
                $sheet->setCellValue("A2", mb_strtoupper(isset($exam->name) ? $exam->name : "", "UTF8"));
            }*/

            $i = 0;
            $row = 3;
            $tongSoNguoi = 0;
            $tongSoLuot = 0;

            $sheet->setCellValue("C3", "Độ tuổi");
            $officeIdBefore = 0;
            $count = count($ages);
            foreach ($data as $d) {
                $officeIdCurrent = $d['id'];
                if ($officeIdCurrent != $officeIdBefore) {
                    $i++;
                    $j = $row + 1;
                    $k = $row + $count;
                    $sheet->setCellValue("A$j", $i);
                    $sheet->mergeCells("A$j:A$k");
                    $sheet->setCellValue("B$j", $d['name']);
                    $sheet->mergeCells("B$j:B$k");
                    if ($agesId == 0) {
                        $sheet->mergeCells("G$j:G" . ($row + $count));
                        $sheet->setCellValue("G$j", "=IF(SUM(F$j:F$k)>0,SUM(F$j:F$k)," . '"")');
                        $sheet->mergeCells("E$j:E$k");
                        $sheet->setCellValue("E$j", "=IF(SUM(D$j:D$k)>0,SUM(D$j:D$k)," . '"")');
                    } else {
                        $sheet->mergeCells("F$j:G$j");
                        $sheet->mergeCells("D$j:E$j");
                    }
                }

                $reLoop = 0;
                $z = 0;
                foreach ($ages as $g) {
                    $z++;
                    if ($officeIdCurrent != $officeIdBefore) {
                        $row++;
                        $sheet->setCellValue("C$row", $g->name);
                    } else {
                        $reLoop = 1;
                    }
                    $soNguoi = $g->id == $d['age_id'] ? $d['so_nguoi_thi'] : 0;
                    if ($soNguoi > 0) {
                        $newRow = $reLoop == 0 ? $row : $row - $count + $z;
                        $sheet->setCellValue("D$newRow", $soNguoi);
                        $soLuot = $g->id == $d['age_id'] ? $d['so_luot_thi'] : 0;
                        $sheet->setCellValue("F$newRow", $soLuot);
                        if ($officeIdCurrent == $officeIdBefore) {
                            break;
                        }
                    }
                }
                if ($agesId == 0) {
                    $tongSoNguoi += (int)$d['so_nguoi_thi'];
                    $tongSoLuot += (int)$d['so_luot_thi'];
                } elseif ($ages[0]->id == $d['age_id']) {
                    $tongSoNguoi += (int)$d['so_nguoi_thi'];
                    $tongSoLuot += (int)$d['so_luot_thi'];
                }
                $officeIdBefore = $officeIdCurrent;
            }

            if ($collectionId == 0) {
                /*** thêm số liệu tổng ***/
                $row++;
                $sheet->mergeCells("A$row:C$row");
                $sheet->setCellValue("A$row", "Tổng");
                $sheet->setCellValue("D$row", $tongSoNguoi);
                $sheet->mergeCells("D$row:E$row");
                $sheet->setCellValue("F$row", $tongSoLuot);
                $sheet->mergeCells("F$row:G$row");
            }

            $sheet->getStyle("A4:G$row")->applyFromArray($style);
        } else {
            $fileName = "tk-so-nguoi-so-luot-thi.xlsx";
            $objPHPExcel = IOFactory::load(resource_path("templates/$fileName"));
            $sheet = $objPHPExcel->getSheet(0);
            $sheet->setShowGridlines(false);
            $sheet->setCellValue("B3", "Đơn vị");

            if ($collectionId == 0) {
                $userOffices = UserOffice::whereNull("parent_id")
                    ->select("id", "name")
                    ->orderBy("name", "asc")->get();
            } else {
                $userOffices = UserOffice::where("parent_id", $collectionId)
                    ->orWhere("id", $collectionId)
                    ->select("id", "name")
                    ->orderBy("parent_id", "desc")->orderBy("name", "asc")->get();
            }

            $data = [];
            foreach ($userOffices as $ufs) {
                $data["-$ufs->id-"] = [
                    "name" => $ufs->name,
                    "so_nguoi_thi" => 0,
                    "so_luot_thi" => 0,
                    "is_parent" => $ufs->id == $collectionId ? 1 : 0,
                    "path" => Str::slug($ufs->name)
                ];
            }

            $sql = "select uo.id, count(distinct rs.user_id) as so_nguoi_thi,
                    count(rs.id) as so_luot_thi, uo.parent_id, 
                    concat('-',uo.path_primary_key,'-') as path_primary_key
                from ex_results rs
                inner join ex_exams ex on ex.id=rs.exam_id
                inner join users u on u.id=rs.user_id
                inner join user_profiles up on up.user_id=u.id
                inner join user_offices uo on uo.id=up.office_id
                where rs.deleted_at is null and u.deleted_at is null and ex.deleted_at is null 
                  and u.organization_id=0 ";

            if ($examId > 0) {
                $sql .= " and rs.exam_id=$examId";
            }

            if ($collectionId > 0) {
                $sql .= " and concat('-', uo.path_primary_key, '-') like '%-$collectionId-%' ";
            }

            $sql .= " group by uo.id";
            $offices = DB::select($sql);

            $index = 1;
            if ($collectionId > 0) {
                $currentOffice = UserOffice::find($collectionId);
                $index = count(explode("-", isset($currentOffice->path_primary_key) ? $currentOffice->path_primary_key : "")) + 1;
                //$data["-$collectionId-"]['name'] = "Tổng";
            }

            $tongNguoiThi = 0;
            $tongLuotThi = 0;
            foreach ($offices as $f) {
                $keys = explode("-", $f->path_primary_key);
                $k = (count($keys) >= $index + 2) ? $keys[$index] : $collectionId;
                $data["-$k-"]['so_nguoi_thi'] += $f->so_nguoi_thi;
                $data["-$k-"]['so_luot_thi'] += $f->so_luot_thi;
                $tongNguoiThi += $f->so_nguoi_thi;
                $tongLuotThi += $f->so_luot_thi;
            }

            if ($collectionId > 0) {
                $data["-$collectionId-"]['so_nguoi_thi'] = $tongNguoiThi;
                $data["-$collectionId-"]['so_luot_thi'] = $tongLuotThi;
            }

            $isParents  = array_column($data, 'is_parent');
            $numOfPersons = array_column($data, 'so_nguoi_thi');
            $numOfSession = array_column($data, 'so_luot_thi');
            $paths = array_column($data, 'path');
            array_multisort(
                $isParents,
                SORT_ASC,
                $numOfPersons,
                SORT_DESC,
                $numOfSession,
                SORT_DESC,
                $paths,
                SORT_ASC,
                $data
            );

            $i = 0;
            $row = 3;
            $tongSoNguoi = 0;
            $tongSoLuot = 0;

            foreach ($data as $d) {
                $row++;
                $i++;
                $soNguoiThi = $d['so_nguoi_thi'];
                $soLuotThi = $d['so_luot_thi'];
                $sheet->setCellValue("A$row", $i);
                $sheet->setCellValue("B$row", $d['name']);
                $sheet->setCellValue("C$row", $soNguoiThi);
                $tongSoNguoi += (int) $soNguoiThi;
                $tongSoLuot += (int) $soLuotThi;
            }

            if ($collectionId == 0) {
                $row++;
                $sheet->mergeCells("A$row:B$row");
                $sheet->setCellValue("A$row", "Tổng");
                $sheet->setCellValue("C$row", $tongSoNguoi);
            }
            $sheet->getStyle("A4:C$row")->applyFromArray($style);
        }

        $fileType = "Html";
        if ($docType == "excel") {
            $fileType = "Xlsx";
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename=' . $fileName);
            header('Cache-Control: max-age=0');
        }

        $objWriter = IOFactory::createWriter($objPHPExcel, $fileType);
        $objWriter->save('php://output');
        exit; // không có lệnh này thì sẽ lỗi khi xuất excel 2007
    }

    // bỏ function này
    public function getDataKetquaDonvi1($exam_id)
    {
        $data = DB::select(
            "SELECT op.*, t.* from user_offices op " .
                " LEFT JOIN (" .
                "SELECT  sum(IF ( tbl.mark is null , 0, tbl.mark )   ) as tongdiem, " .
                " count(DISTINCT(tbl.result_id)) as nguoi_thamgia,  " .
                " sum(tbl.distance) as tongthoigian,  " .
                "p.office_id from (" .
                "SELECT a.* from (  " .
                "select r.id as result_id, r.user_id, r.mark," .
                " TIMESTAMPDIFF(SECOND, r.time_start, r.time_end) as distance " .
                " from ex_results r " .
                "  where r.exam_id= :exam_id and r.guess_number is NOT NULL and deleted_at is null" .
                " and r.id=( " .
                "select r2.id from ex_results r2  " .
                "where r2.exam_id=r.exam_id and r2.user_id=r.user_id  " .
                " order by r2.mark desc, TIMESTAMPDIFF(SECOND, r2.time_start, r2.time_end) asc limit 1) " .
                " ORDER BY r.mark desc, TIMESTAMPDIFF(SECOND, r.time_start, r.time_end) asc, r.user_id ) a  " .
                " LEFT JOIN users u on a.user_id = u.id " .
                " LEFT JOIN user_profiles p on u.id  = p.user_id " .
                " ORDER BY a.mark desc, a.distance asc ) tbl " .
                " join user_profiles p on tbl.user_id = p.user_id" .
                " GROUP BY p.office_id" .
                " ) t on op.id = t.office_id " .
                " order by t.nguoi_thamgia desc, t.tongdiem desc, t.tongthoigian asc, op.id ",
            [
                'exam_id' => $exam_id,
            ]
        );
        return $data;
    }

    public function getDataKetquaDonvi($exam_id)
    {
        $data = DB::select(
            " SELECT op.*, t.* from user_offices op 
           LEFT JOIN 
           (
               SELECT  sum(IF ( tbl.mark is null , 0, tbl.mark )) as tongdiem,
               count(DISTINCT(tbl.result_id)) as nguoi_thamgia,
               sum(tbl.distance) as tongthoigian,  p.office_id 
               from ( 
                       SELECT a.*  from 
                           (  
                               SELECT a.result_id, a.distance, a.mark, a.user_id, a.time_start, 
                               a.time_end, a.guess_number from (
                               SELECT @row_number:=CASE
                                               WHEN @customer_no = a.user_id
                                                   THEN @row_number + 1
                                                   ELSE 1
                                               END AS num,
                                               @customer_no:= a.user_id as num_user_id,
                                               a.*
                               from
                               ( select r.id as result_id, r.user_id, r.mark, 
                                   TIMESTAMPDIFF(SECOND, r.time_start, r.time_end) as distance, 
                                   r.guess_number, r.time_start, r.time_end
                               from ex_results r 
                               where r.exam_id= :exam_id and r.guess_number is NOT NULL and r.deleted_at is null
                               ORDER BY r.user_id,  r.mark desc, TIMESTAMPDIFF(SECOND, r.time_start, r.time_end)  asc
                               ) a,
                                (SELECT @customer_no:=0,@row_number:=0) as t
                               ) a 
                               join (
                                   SELECT DISTINCT(r.user_id) from ex_results r
                                   inner join users ur on r.user_id = ur.id and ur.organization_id=0
                                      and ur.deleted_at is null
                                    where r.exam_id= :exam_id and r.deleted_at is null ORDER BY r.user_id
                               ) b on a.user_id = b.user_id
                               
                               WHERE a.num = 1
                               ORDER BY a.mark desc, a.distance asc
                       ) a  
                       LEFT JOIN users u on a.user_id = u.id and u.organization_id=0 and u.deleted_at is null
                       LEFT JOIN user_profiles p on u.id = p.user_id  
                       ORDER BY a.mark desc, a.distance asc 
                   ) tbl  
                   join user_profiles p on tbl.user_id = p.user_id GROUP BY p.office_id
           ) t on op.id = t.office_id  
           order by t.nguoi_thamgia desc, t.tongdiem desc, t.tongthoigian asc, op.id ",
            [
                'exam_id' => $exam_id,
            ]
        );
        return $data;
    }
    // bỏ function này
    public function getDataKetquaRegion1($exam_id)
    {
        $data = DB::select(
            "SELECT t.name, sum(IF ( t.tongdiem is null , 0, t.tongdiem ) ) as tongdiem, " .
                "sum(t.nguoi_thamgia) as nguoi_thamgia, " .
                "sum(t.tongthoigian) as tongthoigian " .
                " from " .
                "(" .
                "SELECT r.name, r.id, t.* from regions r " .
                "left join " .
                "(" .
                "SELECT tbl.*, r.parent_id from regions r  " .
                "join " .
                "( " .
                "SELECT  sum(IF ( tbl.mark is null , 0, tbl.mark ) ) as tongdiem, " .
                "count(DISTINCT(tbl.result_id)) as nguoi_thamgia, " .
                "sum(tbl.distance) as tongthoigian, " .
                "	p.region_id from " .
                "(	" .
                "SELECT a.* from ( " .
                "select r.id as result_id, r.user_id, r.mark," .
                "  TIMESTAMPDIFF(SECOND, r.time_start, r.time_end) as distance " .
                "from ex_results r " .
                "where r.exam_id= :exam_id and deleted_at is NULL " .
                "and r.id=( " .
                "select r2.id from ex_results r2 " .
                "where r2.exam_id=r.exam_id and r2.user_id=r.user_id " .
                "order by r2.mark desc,  TIMESTAMPDIFF(SECOND, r.time_start, r.time_end) asc limit 1) " .
                "ORDER BY r.mark desc, distance asc, r.user_id ) a " .
                "LEFT JOIN users u on a.user_id = u.id " .
                "LEFT JOIN user_profiles p on u.id  = p.user_id " .
                "LEFT JOIN regions r on r.id  = p.region_id " .
                "ORDER BY a.mark desc, a.distance asc " .
                ") tbl " .
                "join user_profiles p on tbl.user_id = p.user_id " .
                "GROUP BY p.region_id " .
                ") tbl on tbl.region_id = r.id " .
                ") t on t.parent_id = r.id " .
                "where r.parent_id =26 order by t.nguoi_thamgia desc, t.tongdiem desc, t.tongthoigian asc, r.id" .
                ") t group by t.id ORDER BY nguoi_thamgia DESC, tongdiem desc, tongthoigian asc, t.id",
            [
                'exam_id' => $exam_id,
            ]
        );
        return $data;
    }
    public function getDataKetquaRegion($exam_id)
    {
        $provinceId = getSystemConfig('province_id');
        $data = DB::select(
            "SELECT t.name, sum(IF ( t.tongdiem is null , 0, t.tongdiem ) ) as tongdiem,
            sum(t.nguoi_thamgia) as nguoi_thamgia, sum(t.tongthoigian) as tongthoigian
            from (
            
            SELECT r.name, r.id, t.* from regions r
            left join 
            (
            SELECT tbl.*, r.parent_id from regions r  
            join 
            (
                SELECT  sum(IF ( tbl.mark is null , 0, tbl.mark ) ) as tongdiem,
                    count(DISTINCT(tbl.result_id)) as nguoi_thamgia,
                    sum(tbl.distance) as tongthoigian,
                    p.region_id from
                    (	
                            SELECT a.* from 
                            (
                                SELECT a.result_id, a.distance, a.mark, a.user_id,
                                 a.time_start, a.time_end from (
                                SELECT @row_number:=CASE
                                                WHEN @customer_no = a.user_id
                                                    THEN @row_number + 1
                                                    ELSE 1
                                                END AS num,
                                                @customer_no:= a.user_id as num_user_id,
                                                a.*
                                from
                                ( select r.id as result_id, r.user_id, r.mark, 
                                    TIMESTAMPDIFF(SECOND, r.time_start, r.time_end) as distance, 
                                    r.guess_number, r.time_start, r.time_end
                                from ex_results r 
                                where r.exam_id= :exam_id and r.deleted_at is null
                                ORDER BY r.user_id,  r.mark desc, TIMESTAMPDIFF(SECOND, r.time_start, r.time_end)  asc
                                ) a,
                                 (SELECT @customer_no:=0,@row_number:=0) as t
                                ) a 
                                join (
                                    SELECT DISTINCT(r.user_id) from ex_results r 
                                    inner join users ur on r.user_id = ur.id and ur.organization_id=0
                                      and ur.deleted_at is null
                                    where r.exam_id= :exam_id and r.deleted_at is null ORDER BY r.user_id
                                ) b on a.user_id = b.user_id
                                
                                WHERE a.num = 1
                                ORDER BY a.mark desc, a.distance asc
                            ) a 
                            LEFT JOIN users u on a.user_id = u.id and u.organization_id=0 and u.deleted_at is null
                            LEFT JOIN user_profiles p on u.id  = p.user_id
                            LEFT JOIN regions r on r.id  = p.region_id
                            ORDER BY a.mark desc, a.distance asc 
                    ) tbl 
                    join user_profiles p on tbl.user_id = p.user_id
                    GROUP BY p.region_id 
                ) tbl on tbl.region_id = r.id
            ) t on t.parent_id = r.id
            where r.parent_id = :provinceId order by t.nguoi_thamgia desc, t.tongdiem desc, t.tongthoigian asc, r.id 
            ) t group by t.id ORDER BY nguoi_thamgia DESC, tongdiem desc, tongthoigian asc, t.id",
            [
                'exam_id' => $exam_id,
                'provinceId' => $provinceId,
            ]
        );
        return $data;
    }
    public function giaiTapthe(Request $request)
    {
        $orgTypeId = getOrgTypeId();
        if ($orgTypeId == 0) {
            exit;
        }
        $exams = Exam::orderBy('date_start', 'desc')->get();
        return view(
            'manage.statistic.giai_tapthe',
            [
                'exams' => $exams,
            ]
        );
    }
    public function kqGiaiTapthe($type_id, $exam_id)
    {
        $orgTypeId = getOrgTypeId();
        if ($orgTypeId == 0) {
            exit;
        }
        $data = [];
        if ($type_id == 1) {
            $data = $this->getDataKetquaRegion($exam_id);
        } else {
            $data = $this->getDataKetquaDonvi($exam_id);
        }
        return response()->json(['message' => 'success', 'data' => $data]);
    }
    public function giaiCanhan(Request $request)
    {
        $orgTypeId = getOrgTypeId();
        if ($orgTypeId == 0) {
            exit;
        }
        $exams = Exam::orderBy('status', 'desc')->orderBy('date_start', 'desc')->get();
        return view(
            'manage.statistic.giai_canhan',
            [
                'exams' => $exams,
            ]
        );
    }

    public function kqGiaiCanhan($exam_id, $limit = 11)
    {
        $orgTypeId = getOrgTypeId();
        if ($orgTypeId == 0) {
            exit;
        }
        if ($limit == 0) {
            $limit = getExamConfig($exam_id, 'number_of_top_candidates');
        }
        $results = self::getOrderCandidates($exam_id, $limit);
        return view('manage.statistic.tbl_giai_canhan', ['results' => $results, 'examId' => $exam_id]);
    }

    public function exportGiaiCaNhan($exam_id, $limit)
    {
        $fileName = "giai-ca-nhan.xlsx";

        // Tạo file Excel mới thay vì sử dụng template
        $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $objPHPExcel->getActiveSheet();
        $sheet->setShowGridlines(false);

        $exam = Exam::find($exam_id);
        if (!isset($exam)) {
            return ["success" => false, "msg" => "Không tồn tại kỳ thi"];
        }
        $examName = isset($exam->name) ? $exam->name : "";
        $limit = isset($limit) ? (int)$limit : 11;
        $data = self::getOrderCandidates($exam_id, $limit);
        $totalUsers = count($data);

        // Thiết lập tiêu đề
        $sheet->setCellValue("A1", mb_strtoupper("KẾT QUẢ: $examName", "UTF8"));
        $sheet->setCellValue("A2", "Số người tham dự vòng thi: $totalUsers");

        // Thiết lập header
        $sheet->setCellValue("A4", "STT");
        $sheet->setCellValue("B4", "Họ tên");
        $sheet->setCellValue("C4", "Đơn vị");
        $sheet->setCellValue("D4", "Điểm trắc nghiệm");
        $sheet->setCellValue("E4", "Thời gian làm bài (giây)");
        $sheet->setCellValue("F4", "Bắt đầu");
        $sheet->setCellValue("G4", "Kết thúc");

        // Style cho tiêu đề
        $titleStyle = [
            "font" => [
                "bold" => true,
                "size" => 14
            ],
            "alignment" => [
                "horizontal" => Alignment::HORIZONTAL_CENTER,
            ]
        ];
        $sheet->getStyle("A1")->applyFromArray($titleStyle);

        // Merge cells cho tiêu đề
        $sheet->mergeCells("A1:G1");

        // Style cho header
        $headerStyle = [
            "font" => ["bold" => true],
            "fill" => [
                "fillType" => Fill::FILL_SOLID,
                "startColor" => ["rgb" => "E6E6E6"]
            ],
            "borders" => [
                "allBorders" => ["borderStyle" => Border::BORDER_THIN]
            ],
            "alignment" => [
                "horizontal" => Alignment::HORIZONTAL_CENTER,
                "vertical" => Alignment::VERTICAL_CENTER,
            ]
        ];
        $sheet->getStyle("A4:G4")->applyFromArray($headerStyle);

        // Ghi dữ liệu
        $row = 4;
        $markPerQuestion = getExamConfig($exam_id, 'mark_per_multiple_choice_question');
        foreach ($data as $index => $d) {
            $row++;
            $sheet->setCellValue("A$row", $index + 1);
            $sheet->setCellValue("B$row", isset($d->name) ? $d->name : '');
            $sheet->setCellValue("C$row", isset($d->office_name) ? $d->office_name : '');
            $sheet->setCellValue("D$row", (isset($d->mark) ? $d->mark : 0) * $markPerQuestion);
            $sheet->setCellValue("E$row", isset($d->time_testing) ? $d->time_testing : '');
            $sheet->setCellValue("F$row", isset($d->time_start) ? $d->time_start : '');
            $sheet->setCellValue("G$row", isset($d->time_end) ? $d->time_end : '');
        }

        // Style cho toàn bộ bảng dữ liệu
        $dataStyle = [
            "borders" => [
                "allBorders" => ["borderStyle" => Border::BORDER_THIN]
            ],
            "alignment" => [
                "vertical" => Alignment::VERTICAL_CENTER,
            ]
        ];
        $sheet->getStyle("A4:G$row")->applyFromArray($dataStyle);

        // Căn giữa cột STT và Điểm
        $centerStyle = [
            "alignment" => [
                "horizontal" => Alignment::HORIZONTAL_CENTER,
            ]
        ];
        $sheet->getStyle("A5:A$row")->applyFromArray($centerStyle); // STT
        $sheet->getStyle("D5:D$row")->applyFromArray($centerStyle); // Điểm
        $sheet->getStyle("E5:E$row")->applyFromArray($centerStyle); // Thời gian

        // Tự động điều chỉnh độ rộng cột
        foreach (range('A', 'G') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        $fileType = "Xlsx";
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename=' . $fileName);
        header('Cache-Control: max-age=0');
        $objWriter = IOFactory::createWriter($objPHPExcel, $fileType);
        $objWriter->save('php://output');
        exit; // không có lệnh này thì sẽ lỗi khi xuất excel 2007
    }

    /**
     * <AUTHOR> QUAN
     * @param $exam_id
     * @param $limit
     * @return array
     */
    public function getOrderCandidates($exam_id, $limit)
    {
        $isExist = ExamResultFinal::where('exam_id', $exam_id)->exists();
        $data = [];
        if ($isExist && $limit == getExamConfig($exam_id, 'number_of_top_candidates')) {
            $data = ExamResultFinal::where('exam_id', $exam_id)
                ->selectRaw("name, address, mark, literature_mark, office_name, user_id,
                    DATE_FORMAT(time_start, '%d/%m/%Y %H:%i:%s') as time_start,
                    DATE_FORMAT(time_end, '%d/%m/%Y %H:%i:%s') as time_end, result_id,
                    time_start as raw_time_start, time_end as raw_time_end,
                    timestampdiff(second, time_start, time_end) as time_testing")
                ->orderBy("id", "asc")->limit($limit)->get();
        } else {
            $data = self::getRankingCandidates($exam_id, $limit);
        }
        return $data;
    }

    /**
     * <AUTHOR> QUAN
     * Hàm này dùng cho trường hợp thí sinh được làm bài thi n lần, n>=1
     */
    public function getRankingCandidates($exam_id, $limit)
    {
        $exam_id = (int)$exam_id;
        $limit = (int)$limit;
        $maxTimeQuiz = getExamConfig($exam_id, 'max_time_quiz');
        $sql = "select users.name, rs2.mark, user_offices.name as office_name, user_profiles.address,
                rs2.user_id, date_format(rs2.time_start, '%d/%m/%Y %H:%i:%s') as time_start,
                date_format(rs2.time_end, '%d/%m/%Y %H:%i:%s') as time_end, rs2.result_id,
                rs2.time_start as raw_time_start, rs2.time_end as raw_time_end, rs2.time_testing
            from (
                select row_number() over (
                    partition by user_id order by user_id desc, ex_results.mark desc,
                      timestampdiff(second, ex_results.time_start, ex_results.time_end) asc,
                      ex_results.time_start asc
                    ) row_num, ex_results.id as result_id, ex_results.user_id, ex_results.mark,
                    ifnull(ex_results.time_end, ex_results.updated_at) as time_end, ex_results.time_start,
                    timestampdiff(second, ex_results.time_start,
                      ifnull(ex_results.time_end, ex_results.updated_at)) as time_testing
                from ex_results where (ex_results.status=1 or ex_results.time_end is not null 
                    or TIMESTAMPDIFF(MINUTE, ex_results.time_start, NOW())>=$maxTimeQuiz)
                and ex_results.exam_id=$exam_id) rs2
            inner join users on users.id=rs2.user_id
            inner join user_profiles on user_profiles.user_id=users.id
            inner join user_offices on user_offices.id=user_profiles.office_id
            where rs2.row_num=1 and users.deleted_at is null and users.organization_id=0
            order by rs2.mark desc, rs2.time_testing asc, rs2.time_start asc limit $limit";
        $data = DB::select($sql);
        return $data;
    }

    public function homeManageStatistic($examId)
    {
        $data = [];

        $groups = Exam::join("ex_results", "ex_results.exam_id", "=", "ex_exams.id")
            ->join("users", "users.id", "=", "ex_results.user_id")
            ->join("user_profiles", "user_profiles.user_id", "=", "users.id")
            ->join("user_groups", "user_groups.id", "=", "user_profiles.group_id")
            ->whereNull("ex_results.deleted_at")->whereNull("users.deleted_at")
            ->where("users.organization_id", 0)->where("ex_results.exam_id", $examId)
            ->selectRaw("user_groups.name, count(DISTINCT users.id) as so_luong")
            ->groupBy("user_groups.name")->get();
        foreach ($groups as $g) {
            $data[] = [
                "type_id" => 1,
                "exam_id" => $examId,
                "name" => $g->name,
                "quantity" => $g->so_luong
            ];
        }

        $ages = DB::table("grade_ages")->orderBy('id', 'asc')->get();
        $count = count($ages);
        $sqlAges = "";
        foreach ($ages as $k => $v) {
            if ($k == 0) {
                $sqlAges .= " sum(if(YEAR(NOW()) - YEAR(date_of_birth) <= $v->max, 1, 0)) as grade_age_$v->id, ";
            } else {
                if ($k < $count - 2) {
                    $sqlAges .= " sum(if(YEAR(NOW()) - YEAR(date_of_birth) >= $v->min
                            and YEAR(NOW()) - YEAR(date_of_birth) <= $v->max, 1, 0)) as grade_age_$v->id, ";
                } else {
                    $sqlAges .= $k == $count - 2 ?
                        " sum(if(YEAR(NOW()) - YEAR(date_of_birth) >= $v->min, 1, 0)) as grade_age_$v->id, "
                        : " sum(if(date_of_birth is null, 1, 0)) as grade_age_$v->id ";
                }
            }
        }

        $sql = "select $sqlAges
            from (
                select DISTINCT users.id, date_of_birth
                from ex_exams
                inner join ex_results on ex_results.exam_id=ex_exams.id
                inner join users on users.id=ex_results.user_id
                inner join user_profiles on user_profiles.user_id=users.id
                where ex_exams.deleted_at is null and ex_results.deleted_at is null
                and users.deleted_at is null and users.organization_id=0 and ex_exams.id=$examId
            ) tb";
        $ageGrades = DB::select($sql);
        foreach ($ages as $age) {
            $data[] = [
                "type_id" => 2,
                "exam_id" => $examId,
                "name" => $age->name,
                "quantity" => $ageGrades[0]->{"grade_age_".$age->id}
            ];
        }

        $provinceId = getSystemConfig('province_id');
        $areas = DB::select("select sum(kieu_bao) as nuoc_ngoai,
            sum(trong_tinh) as trong_tinh, sum(ngoai_tinh) as ngoai_tinh
            from (
            select if(g.name='Kiều bào',1,0) as kieu_bao,
            if(r.path_primary_key like '$provinceId/%' and g.name!='Kiều bào',1,0) as trong_tinh, 
            if(r.path_primary_key not like '$provinceId/%' and g.name!='Kiều bào',1,0) as ngoai_tinh 
            from ex_results rs 
            inner join ex_exams ex on ex.id=rs.exam_id 
            inner join users u on u.id=rs.user_id 
            inner join user_profiles up on up.user_id=u.id
            inner join user_groups g on g.id=up.group_id 
            inner join regions r on r.id=up.region_id
            where rs.deleted_at is null and u.deleted_at is null and ex.deleted_at is null and u.organization_id=0
            and r.level=3 and ex.id=$examId group by u.id) tb");
        $data[] = [
            "type_id" => 3,
            "exam_id" => $examId,
            "name" => "Trong tỉnh",
            "quantity" => isset($areas[0]->trong_tinh) ? $areas[0]->trong_tinh : 0
        ];
        $data[] = [
            "type_id" => 3,
            "exam_id" => $examId,
            "name" => "Ngoài tỉnh",
            "quantity" => isset($areas[0]->ngoai_tinh) ? $areas[0]->ngoai_tinh : 0
        ];
        $data[] = [
            "type_id" => 3,
            "exam_id" => $examId,
            "name" => "Nước ngoài",
            "quantity" => isset($areas[0]->nuoc_ngoai) ? $areas[0]->nuoc_ngoai : 0
        ];

        DB::beginTransaction();
        ExamStatistic::where("exam_id", $examId)->delete();
        ExamStatistic::insert($data);
        DB::commit();
        return ["success" => true];
    }

    public function statisticAwardOfOffices($examId, $awardTypeId, $docType)
    {
        switch ($awardTypeId) {
            case 0:
                self::statisticByOffices($examId, 0, -1, $docType);
                break;
            case 1:
                self::statisticOfficesByQuality($examId, $docType);
                break;
            case 2:
                self::statisticOfficesByRatio($examId, $docType);
                break;
        }
    }

    /** Thống kê tập thể theo tỉ lệ thí sinh tham gia **/
    public function statisticOfficesByRatio($examId, $docType)
    {
        $sql = "select user_offices.name, count(DISTINCT users.id) as so_thi_sinh, user_offices.number_of_staffs
            from ex_results 
            inner join users on users.id=ex_results.user_id
            inner join user_profiles on user_profiles.user_id=users.id
            inner join user_offices on user_offices.id=user_profiles.office_id
            where ex_results.exam_id=$examId and users.deleted_at is null and users.organization_id=0
            and ex_results.deleted_at is null and user_offices.number_of_staffs>0
            group by user_offices.name, user_offices.number_of_staffs
            order by (count(DISTINCT users.id)/user_offices.number_of_staffs) desc";
        $data = DB::select($sql);

        $fileName = "tk-tap-the-theo-ti-le-thi-sinh-tham-gia.xlsx";
        $objPHPExcel = IOFactory::load(resource_path("templates/$fileName"));
        $sheet = $objPHPExcel->getSheet(0);
        $sheet->setShowGridlines(false);

        $row = 3;
        foreach ($data as $d) {
            $row ++;
            $sheet->setCellValue("A$row", $row-3);
            $sheet->setCellValue("B$row", $d->name);
            $sheet->setCellValue("C$row", $d->number_of_staffs);
            $sheet->setCellValue("D$row", $d->so_thi_sinh);
            $sheet->setCellValue("E$row", "=ROUND(D$row*100/C$row,2)");
        }

        $style = [
            "borders" => [
                "allBorders" => ["borderStyle" => Border::BORDER_THIN]
            ]
        ];
        $sheet->getStyle("A4:E$row")->applyFromArray($style);

        $fileType = "Html";
        if ($docType == "excel") {
            $fileType = "Xlsx";
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename=' . $fileName);
            header('Cache-Control: max-age=0');
        }
        $objWriter = IOFactory::createWriter($objPHPExcel, $fileType);
        $objWriter->save('php://output');
        exit; // không có lệnh này thì sẽ lỗi khi xuất excel 2007
    }

    /** Thống kê tập thể theo chất lượng các bài dự thi tốt nhất **/
    public function statisticOfficesByQuality($examId, $docType)
    {
        // lấy điểm trắc nghiệm cao nhất
        $maxMultipleChoice = ExamResult::join("users", "users.id", "=", "ex_results.user_id")
            ->where("ex_results.exam_id", $examId)
            ->whereNull("users.deleted_at")->where("users.organization_id", 0)
            ->max('ex_results.mark');
        $maxMultipleChoice = (int)$maxMultipleChoice;

        // lấy điểm tự luận cao nhất trong số những bài có điểm trắc nghiệm cao nhất
        $maxLiterature = ExamResult::join("users", "users.id", "=", "ex_results.user_id")
            ->where("ex_results.exam_id", $examId)->where("ex_results.mark", $maxMultipleChoice)
            ->whereNull("users.deleted_at")->where("users.organization_id", 0)
            ->max('ex_results.literature_mark');
        $maxLiterature = (int)$maxLiterature;

        $sql = "select user_offices.name, count(DISTINCT users.id) as so_thi_sinh
        from ex_results 
        inner join users on users.id=ex_results.user_id
        inner join user_profiles on user_profiles.user_id=users.id
        inner join user_offices on user_offices.id=user_profiles.office_id
        where ex_results.exam_id=$examId and users.deleted_at is null and users.organization_id=0
        and ex_results.deleted_at is null and user_offices.number_of_staffs>0
        and ex_results.mark=$maxMultipleChoice and ex_results.literature_mark=$maxLiterature
        group by user_offices.name order by count(DISTINCT users.id) desc";

        $data = DB::select($sql);
        $fileName = "tk-so-nguoi-so-luot-thi.xlsx";
        $objPHPExcel = IOFactory::load(resource_path("templates/$fileName"));
        $sheet = $objPHPExcel->getSheet(0);
        $sheet->setShowGridlines(false);
        $markPerMultipleChoiceQuestion = getExamConfig($examId, 'mark_per_multiple_choice_question');
        $maxMulChoice = $maxMultipleChoice*$markPerMultipleChoiceQuestion;
        $note = "(Thống kê những bài thi đạt $maxMulChoice điểm trắc nghiệm và $maxLiterature điểm tự luận)";
        $sheet->setCellValue("A2", $note);
        $sheet->setCellValue("C3", "Số bài thi");
        $row = 3;
        foreach ($data as $d) {
            $row ++;
            $sheet->setCellValue("A$row", $row-3);
            $sheet->setCellValue("B$row", $d->name);
            $sheet->setCellValue("C$row", $d->so_thi_sinh);
        }

        $style = [
            "borders" => [
                "allBorders" => ["borderStyle" => Border::BORDER_THIN]
            ]
        ];
        $sheet->getStyle("A4:C$row")->applyFromArray($style);

        $fileType = "Html";
        if ($docType == "excel") {
            $fileType = "Xlsx";
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename=' . $fileName);
            header('Cache-Control: max-age=0');
        }
        $objWriter = IOFactory::createWriter($objPHPExcel, $fileType);
        $objWriter->save('php://output');
        exit; // không có lệnh này thì sẽ lỗi khi xuất excel 2007
    }
}
