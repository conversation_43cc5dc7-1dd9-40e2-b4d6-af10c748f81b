@extends('layouts.app')

@section('title')
Thống kê
@endsection

@section('decription')

@endsection

@push('styles')
<style>
    .box-header {
        border-bottom: 1px solid #80808038;
        display: flex;
        align-items: center
    }

    .box-header button {
        border: none;
        padding: 6px 10px;
        border-radius: 4px;
        background: #3c8dbc;
        color: white;
        margin-left: 10px;
    }

    .box-header>.select2 {
        min-width: 200px !important;
    }

    .my-custom-scrollbar {
        position: relative;
        height: 500px;
        overflow: auto;
    }

    .table-wrapper-scroll-y {
        display: block;
    }
</style>
@endpush

@section('content')
<div class="box">
    <div class="box-header">
        <span style="margin: 0px 5px 0px">Kỳ thi:</span>
        <select id="exam_id">
            {{--<option value="0">-- Chọn kỳ thi --</option>--}}
            @foreach($exams as $ex)
            <option value="{{$ex->id}}" data-public="{{$ex->is_public}}">{{$ex->name}}</option>
            @endforeach
        </select>
        <span style="margin: 0px 5px 0px 15px">Top thí sinh:</span>
        <select id="limit">
            <option value="0">Top thí sinh đạt giải</option>
            <option value="30">Top 30 thí sinh</option>
            <option value="50">Top 50 thí sinh</option>
        </select>
        <button id="btnView"><i class="fa"></i> Thống kê</button>
        <button id="btnExport"><i class="fa fa-file-excel-o"></i> Xuất Excel</button>
        <!-- <button id="btnPublic"><i class="fa fa-times"></i> Hủy công bố</button> -->
    </div>
    <div class="box-body">
        <table class="table table-bordered table-striped table-hover" id="tblResult" width="100%">
            <tr role="row" class="heading">
                <th style="text-align: center">#</th>
                <th>Họ tên</th>
                <th>Đơn vị</th>
                <th style="text-align: center">Điểm trắc nghiệm</th>
                <th style="text-align: center">Thời gian làm bài thi (giây)</th>
                <th style="text-align: center">Bắt đầu</th>
                <th style="text-align: center">Kết thúc</th>
                <th></th>
            </tr>
            <tbody id="resultDetail"></tbody>
        </table>
    </div>
</div>
@endsection
@push('scripts')
<script>
    $('#exam_id').select2();
    $('#limit').select2();
    $(function() {
        if ($('#limit').val() == "11") {
            $('.top_limit').css('display', "none");
        } else
            $('.top_limit').css('display', "block");
    });

    function getGiaiCaNhan() {
        var examId =  $('#exam_id').val();
        if(examId == 0) {
            toastr.info("Bạn chưa chọn kỳ thi");
            return;
        }
        var urlReport = "{{url('/')}}" + "/manage/giai-canhan/" + examId + "/" + $('#limit').val();
        $.ajax({
            url: urlReport,
            method: 'GET',
            // data: form_data,
            contentType: false,
            processData: false,
        }).done(function(rs) {
            $("#resultDetail").html(rs);
        });
    }

    getGiaiCaNhan();

    $("#btnView").click(function() {
        getGiaiCaNhan();
    });

    $("#exam_id, #limit").change(function () {
        getGiaiCaNhan("html");
    });

    $("#btnExport").click(function() {
        var examId =  $('#exam_id').val();
        if(examId == 0) {
            toastr.info("Bạn chưa chọn kỳ thi");
            return;
        }
        var urlExport = "{{url('/')}}" + "/manage/giai-canhan/" + examId + "/" + $('#limit').val() +"/export";
        window.location.href = urlExport;
    });
</script>
@endpush